<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IssueComment extends Model
{
    /** @use HasFactory<\Database\Factories\IssueCommentFactory> */
    use HasFactory;

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function issue() {
        return $this->belongsTo(Issue::class);
    }
}
