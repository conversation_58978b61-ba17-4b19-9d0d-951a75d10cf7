<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageLog extends Model
{
    /** @use HasFactory<\Database\Factories\MessageLogFactory> */
    use HasFactory;

    public function reservation() {
        return $this->belongsTo(Reservation::class);
    }

    public function messageRule() {
        return $this->belongsTo(MessageRule::class);
    }
}
