<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageRule extends Model
{
    /** @use HasFactory<\Database\Factories\MessageRuleFactory> */
    use HasFactory;

    public function listing() {
        return $this->belongsTo(Listing::class);
    }

    public function logs() {
        return $this->hasMany(MessageLog::class);
    }
}
