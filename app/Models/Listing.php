<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Listing extends Model
{
    /** @use HasFactory<\Database\Factories\ListingFactory> */
    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    public function messageRules()
    {
        return $this->hasMany(MessageRule::class);
    }

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function __toString(): string
    {
        return $this->title;
    }
}
