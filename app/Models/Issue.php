<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Issue extends Model
{
    /** @use HasFactory<\Database\Factories\IssueFactory> */
    use HasFactory;

        public function user() {
        return $this->belongsTo(User::class);
    }

    public function comments() {
        return $this->hasMany(IssueComment::class);
    }
}
