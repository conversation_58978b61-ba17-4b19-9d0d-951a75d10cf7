<?php

namespace App\Enums;

class PermissionsEnum
{
     // Listings
    public const VIEW_LISTINGS = 'view listings';
    public const CREATE_LISTINGS = 'create listings';
    public const UPDATE_LISTINGS = 'update listings';
    public const DELETE_LISTINGS = 'delete listings';

    // Reservations
    public const VIEW_RESERVATIONS = 'view reservations';
    public const MODIFY_RESERVATIONS = 'modify reservations';
    public const CANCEL_RESERVATIONS = 'cancel reservations';

    // Tasks
    public const VIEW_TASKS = 'view tasks';
    public const UPDATE_TASKS = 'update tasks';
    public const ASSIGN_TASKS = 'assign tasks';

    // Issues
    public const CREATE_ISSUES = 'create issues';
    public const COMMENT_ON_ISSUES = 'comment on issues';
    public const RESOLVE_ISSUES = 'resolve issues';

    // Admin tools
    public const IMPERSONATE_USERS = 'impersonate users';
    public const ACCESS_NOVA_DASHBOARD = 'access nova dashboard';
    public const VIEW_MESSAGE_LOGS = 'view message logs';
}
