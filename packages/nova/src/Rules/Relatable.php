<?php

namespace <PERSON><PERSON>\Nova\Rules;

use Illuminate\Contracts\Validation\Rule;
use <PERSON><PERSON>\Nova\Fields\HasOne;
use <PERSON>vel\Nova\Fields\MorphOne;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Nova;

class Relatable implements Rule
{
    /**
     * The request instance.
     *
     * @var \Laravel\Nova\Http\Requests\NovaRequest
     */
    public $request;

    /**
     * The query builder instance.
     *
     * @var \Illuminate\Database\Eloquent\Builder
     */
    public $query;

    /**
     * Create a new rule instance.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return void
     */
    public function __construct(NovaRequest $request, $query)
    {
        $this->query = $query;
        $this->request = $request;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $model = $this->query->tap(function ($query) {
            tap($query->getQuery(), function ($builder) {
                /** @var \Illuminate\Database\Query\Builder $builder */
                $builder->orders = [];

                $builder->select(
                    ! empty($builder->joins) ? $builder->from.'.*' : '*'
                );
            });
        })->whereKey($value)->first();

        if (! $model) {
            return false;
        }

        if ($this->relationshipIsFull($model, $attribute, $value)) {
            return false;
        }

        if ($resource = Nova::resourceForModel($model)) {
            return $this->authorize($resource, $model);
        }

        return true;
    }

    /**
     * Determine if the relationship is "full".
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    protected function relationshipIsFull($model, $attribute, $value)
    {
        $inverseRelation = $this->request->newResource()
                    ->resolveInverseFieldsForAttribute($this->request, $attribute)->first(function ($field) {
                        return ($field instanceof MorphOne || $field instanceof HasOne) && ! $field->ofManyRelationship();
                    });

        if ($inverseRelation && $this->request->resourceId) {
            $modelBeingUpdated = $this->request->findModelOrFail();

            if (is_null($modelBeingUpdated->{$attribute})) {
                return false;
            }

            if ($modelBeingUpdated->{$attribute}->getKey() == $value) {
                return false;
            }
        }

        return $inverseRelation &&
               $model->{$inverseRelation->attribute}()->count() > 0;
    }

    /**
     * Authorize that the user is allowed to relate this resource.
     *
     * @param  class-string<\Laravel\Nova\Resource>  $resourceClass
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return bool
     */
    protected function authorize($resourceClass, $model)
    {
        return (new $resourceClass($model))->authorizedToAdd(
            $this->request, $this->request->model()
        );
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('nova::validation.relatable');
    }
}
