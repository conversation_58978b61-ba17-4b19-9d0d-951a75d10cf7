<?php

namespace {{ namespace }};

use Illuminate\Support\Carbon;
use <PERSON>vel\Nova\Filters\DateFilter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class {{ class }} extends DateFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        $value = Carbon::parse($value);

        return $query;
    }
}
