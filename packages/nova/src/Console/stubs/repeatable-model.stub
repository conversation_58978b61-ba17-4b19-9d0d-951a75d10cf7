<?php

namespace {{ namespace }};

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Repeater\Repeatable;

class {{ class }} extends Repeatable
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\{{ namespacedModel }}>
     */
    public static $model = \{{ namespacedModel }}::class;


    /**
     * Get the fields displayed by the repeatable.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            // ID::hidden(),
        ];
    }
}
