<?php

namespace {{ namespace }};

use <PERSON>vel\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class {{ class }} extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = '{{ component }}';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return [];
    }
}
