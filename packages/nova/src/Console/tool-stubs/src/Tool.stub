<?php

namespace {{ namespace }};

use Illuminate\Http\Request;
use Lara<PERSON>\Nova\Menu\MenuSection;
use <PERSON><PERSON>\Nova\Nova;
use <PERSON><PERSON>\Nova\Tool;

class {{ class }} extends Tool
{
    /**
     * Perform any tasks that need to happen when the tool is booted.
     *
     * @return void
     */
    public function boot()
    {
        Nova::script('{{ component }}', __DIR__.'/../dist/js/tool.js');
        Nova::style('{{ component }}', __DIR__.'/../dist/css/tool.css');
    }

    /**
     * Build the menu that renders the navigation links for the tool.
     *
     * @param  \Illuminate\Http\Request $request
     * @return mixed
     */
    public function menu(Request $request)
    {
        return MenuSection::make('{{ title }}')
            ->path('/{{ name }}')
            ->icon('server');
    }
}
