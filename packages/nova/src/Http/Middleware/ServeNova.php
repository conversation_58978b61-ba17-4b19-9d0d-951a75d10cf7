<?php

namespace <PERSON><PERSON>\Nova\Http\Middleware;

use <PERSON><PERSON>\Nova\Events\NovaServiceProviderRegistered;
use <PERSON>vel\Nova\Util;

class ServeNova
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request):mixed  $next
     * @return \Illuminate\Http\Response
     */
    public function handle($request, $next)
    {
        if (Util::isNovaRequest($request)) {
            NovaServiceProviderRegistered::dispatch();
        }

        return $next($request);
    }
}
