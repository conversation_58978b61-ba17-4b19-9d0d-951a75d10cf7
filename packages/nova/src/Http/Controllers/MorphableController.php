<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON>vel\Nova\Contracts\RelatableField;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Nova;

class MorphableController extends Controller
{
    /**
     * List the available morphable resources for a given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function __invoke(NovaRequest $request)
    {
        $relatedResource = Nova::resourceForKey($request->type);

        abort_if(is_null($relatedResource), 403);

        $field = $request->newResource()
                        ->availableFieldsOnIndexOrDetail($request)
                        ->whereInstanceOf(RelatableField::class)
                        ->findFieldByAttribute($request->field, function () {
                            abort(404);
                        })->applyDependsOn($request);

        $withTrashed = $this->shouldIncludeTrashed(
            $request, $relatedResource
        );

        $limit = $relatedResource::usesScout()
                    ? $relatedResource::$scoutSearchResults
                    : $relatedResource::$relatableSearchResults;

        $shouldReorderAssociatableValues = $field->shouldReorderAssociatableValues($request) && ! $relatedResource::usesScout();

        $query = method_exists($field, 'searchMorphableQuery')
            ? $field->searchMorphableQuery($request, $relatedResource, $withTrashed)
            : $field->buildMorphableQuery($request, $relatedResource, $withTrashed);

        return [
            'resources' => $query->take($limit)
                                ->get()
                                ->mapInto($relatedResource)
                                ->filter->authorizedToAdd($request, $request->model())
                                ->map(function ($resource) use ($request, $field, $relatedResource) {
                                    return $field->formatMorphableResource($request, $resource, $relatedResource);
                                })->when($shouldReorderAssociatableValues, function ($collection) {
                                    return $collection->sortBy('display');
                                })->values(),
            'withTrashed' => $withTrashed,
            'softDeletes' => $relatedResource::softDeletes(),
        ];
    }

    /**
     * Determine if the query should include trashed models.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  string  $associatedResource
     * @return bool
     */
    protected function shouldIncludeTrashed(NovaRequest $request, $associatedResource)
    {
        if ($request->withTrashed === 'true') {
            return true;
        }

        $associatedModel = $associatedResource::newModel();

        if ($request->current && empty($request->search) && $associatedResource::softDeletes()) {
            $associatedModel = $associatedModel->newQueryWithoutScopes()->find($request->current);

            return $associatedModel ? $associatedModel->trashed() : false;
        }

        return false;
    }
}
