<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON><PERSON>\Nova\GlobalSearch;
use <PERSON>vel\Nova\Http\Requests\GlobalSearchRequest;
use <PERSON><PERSON>\Nova\Nova;

class SearchController extends Controller
{
    /**
     * Get the global search results for the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\GlobalSearchRequest  $request
     * @return array
     */
    public function __invoke(GlobalSearchRequest $request)
    {
        return (new GlobalSearch(
            $request, Nova::globallySearchableResources($request)
        ))->get();
    }
}
