<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use Laravel\Nova\Http\Requests\ResourceDetailRequest;
use <PERSON>vel\Nova\Http\Resources\DetailViewResource;

class ResourceShowController extends Controller
{
    /**
     * Display the resource for administration.
     *
     * @param  \Laravel\Nova\Http\Requests\ResourceDetailRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(ResourceDetailRequest $request)
    {
        return DetailViewResource::make()->toResponse($request);
    }
}
