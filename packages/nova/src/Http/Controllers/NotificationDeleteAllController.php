<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON><PERSON>\Nova\Http\Requests\NotificationRequest;
use <PERSON><PERSON>\Nova\Notifications\Notification;
use <PERSON>vel\Nova\Nova;

class NotificationDeleteAllController extends Controller
{
    /**
     * Delete all notifications.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(NotificationRequest $request)
    {
        Notification::whereNotifiableId(Nova::user($request)->getKey())->delete();

        return response()->json();
    }
}
