<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON><PERSON>\Nova\DeleteField;
use <PERSON>vel\Nova\Http\Requests\PivotFieldDestroyRequest;
use <PERSON>vel\Nova\Nova;

class PivotFieldDestroyController extends Controller
{
    /**
     * Delete the file at the given field.
     *
     * @param  \Laravel\Nova\Http\Requests\PivotFieldDestroyRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(PivotFieldDestroyRequest $request)
    {
        $request->authorizeForAttachment();

        DeleteField::forRequest(
            $request, $request->findFieldOrFail(),
            $pivot = $request->findPivotModel()
        )->save();

        Nova::usingActionEvent(function ($actionEvent) use ($request, $pivot) {
            $actionEvent->forAttachedResourceUpdate(
                $request, $request->findModelOrFail(), $pivot
            )->save();
        });

        return response()->noContent(200);
    }
}
