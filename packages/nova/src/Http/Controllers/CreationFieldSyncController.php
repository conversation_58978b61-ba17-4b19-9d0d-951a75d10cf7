<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illum<PERSON>\Routing\Controller;
use <PERSON><PERSON>\Nova\Http\Requests\ResourceCreateOrAttachRequest;
use <PERSON>vel\Nova\Http\Resources\CreateViewResource;
use <PERSON>vel\Nova\Http\Resources\ReplicateViewResource;

class CreationFieldSyncController extends Controller
{
    /**
     * Synchronize the field for creation view.
     *
     * @param  \Laravel\Nova\Http\Requests\ResourceCreateOrAttachRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(ResourceCreateOrAttachRequest $request)
    {
        $resource = $request->has('fromResourceId')
                        ? ReplicateViewResource::make($request->fromResourceId)->newResourceWith($request)
                        : CreateViewResource::make()->newResourceWith($request);

        return response()->json(
            $resource->creationFields($request)
                ->filter(function ($field) use ($request) {
                    return $request->query('field') === $field->attribute &&
                            $request->query('component') === $field->dependentComponentKey();
                })->each->syncDependsOn($request)
                ->first()
        );
    }
}
