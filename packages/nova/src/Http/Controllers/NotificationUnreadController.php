<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON><PERSON>\Nova\Http\Requests\NotificationRequest;
use <PERSON><PERSON>\Nova\Notifications\Notification;

class NotificationUnreadController extends Controller
{
    /**
     * Mark the given notification as unread.
     *
     * @param  \Laravel\Nova\Http\Requests\NotificationRequest  $request
     * @param  int|string  $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(NotificationRequest $request, $notification)
    {
        $notification = Notification::query()
            ->whereNotifiableId($request->user()->getKey())
            ->findOrFail($notification);

        $notification->update(['read_at' => null]);

        return response()->json();
    }
}
