<?php

namespace <PERSON><PERSON>\Nova\Filters;

use JsonSerializable;
use <PERSON>vel\Nova\AuthorizedToSee;
use <PERSON><PERSON>\Nova\Contracts\Filter as FilterContract;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Makeable;
use <PERSON><PERSON>\Nova\Metable;
use <PERSON><PERSON>\Nova\Nova;
use <PERSON><PERSON>\Nova\ProxiesCanSeeToGate;

abstract class Filter implements FilterContract, JsonSerializable
{
    use AuthorizedToSee;
    use Makeable;
    use Metable;
    use ProxiesCanSeeToGate;

    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name;

    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    abstract public function apply(NovaRequest $request, $query, $value);

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the component name for the filter.
     *
     * @return string
     */
    public function component()
    {
        return $this->component;
    }

    /**
     * Get the displayable name of the filter.
     *
     * @return string
     */
    public function name()
    {
        return $this->name ?: Nova::humanize($this);
    }

    /**
     * Get the key for the filter.
     *
     * @return string
     */
    public function key()
    {
        return get_class($this);
    }

    /**
     * Set the default options for the filter.
     *
     * @return array|mixed
     */
    public function default()
    {
        return '';
    }

    /**
     * Prepare the filter for JSON serialization.
     *
     * @return array<string, mixed>
     */
    public function jsonSerialize(): array
    {
        return array_merge([
            'class' => $this->key(),
            'name' => $this->name(),
            'component' => $this->component(),
            'options' => collect($this->options(app(NovaRequest::class)))->map(function ($value, $label) {
                if (is_array($value)) {
                    return array_merge(['label' => $label], $value);
                } elseif (is_string($label)) {
                    return ['label' => $label, 'value' => $value];
                }

                return ['label' => $value, 'value' => $value];
            })->values()->all(),
            'currentValue' => $this->default() ?? '',
        ], $this->meta());
    }
}
