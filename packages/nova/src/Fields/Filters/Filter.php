<?php

namespace <PERSON><PERSON>\Nova\Fields\Filters;

use <PERSON><PERSON>\Nova\Contracts\FilterableField;
use <PERSON><PERSON>\Nova\Filters\Filter as BaseFilter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

abstract class Filter extends BaseFilter
{
    /**
     * The filter's field.
     *
     * @var \Laravel\Nova\Contracts\FilterableField&\Laravel\Nova\Fields\Field
     */
    public $field;

    /**
     * Construct a new filter.
     *
     * @param  \Laravel\Nova\Contracts\FilterableField&\Laravel\Nova\Fields\Field  $field
     */
    public function __construct(FilterableField $field)
    {
        $this->field = $field;
    }

    /**
     * Get the displayable name of the filter.
     *
     * @return string
     */
    public function name()
    {
        return $this->field->name;
    }

    /**
     * Get the key for the filter.
     *
     * @return string
     */
    public function key()
    {
        return class_basename($this->field).':'.$this->field->attribute;
    }

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        $this->field->applyFilter($request, $query, $value);

        return $query;
    }

    /**
     * Prepare the field for JSON serialization.
     *
     * @return array
     */
    public function serializeField()
    {
        return $this->field->serializeForFilter();
    }

    /**
     * Prepare the filter for JSON serialization.
     *
     * @return array<string, mixed>
     */
    public function jsonSerialize(): array
    {
        return array_merge(parent::jsonSerialize(), [
            'component' => 'filter-'.$this->component,
            'field' => $this->serializeField(),
        ]);
    }
}
