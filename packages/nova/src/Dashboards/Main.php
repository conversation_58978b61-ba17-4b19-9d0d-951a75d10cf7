<?php

namespace <PERSON><PERSON>\Nova\Dashboards;

use Illuminate\Support\Str;
use <PERSON><PERSON>\Nova\Cards\Help;
use <PERSON><PERSON>\Nova\Dashboard;
use <PERSON><PERSON>\Nova\Nova;

class Main extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return class_basename($this);
    }

    /**
     * Get the URI key of the dashboard.
     *
     * @return string
     */
    public function uriKey()
    {
        return Str::snake(class_basename($this));
    }

    /**
     * Get the cards that should be displayed on the Nova dashboard.
     *
     * @return array
     */
    public function cards()
    {
        return [
            new Help,
        ];
    }
}
