<?php

namespace <PERSON><PERSON>\Nova\Testing\Browser\Pages;

use <PERSON><PERSON>\Dusk\Browser;

class NotFound extends Page
{
    /**
     * Create a new page instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct('/404');
    }

    /**
     * Assert that the browser is on the page.
     *
     * @param  \Laravel\Dusk\Browser  $browser
     * @return void
     */
    public function assert(Browser $browser)
    {
        $browser->whenAvailable('@404-error-page', function ($browser) {
            $browser->assertSee('404');
        });
    }
}
