<?php

namespace Lara<PERSON>\Nova\Contracts;

interface Storable
{
    /**
     * Get the disk that the field is stored on.
     *
     * @return string|null
     */
    public function getStorageDisk();

    /**
     * Get the default disk for the field.
     *
     * @return string
     */
    public function getDefaultStorageDisk();

    /**
     * Get the dir that the field is stored at on disk.
     *
     * @return string|null
     */
    public function getStorageDir();

    /**
     * Get the full path that the field is stored at on disk.
     *
     * @return string|null
     */
    public function getStoragePath();
}
