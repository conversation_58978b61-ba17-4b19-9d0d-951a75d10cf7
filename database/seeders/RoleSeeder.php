<?php

namespace Database\Seeders;

use App\Enums\PermissionsEnum;
use App\Models\User;
use App\Enums\RolesEnum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define permissions
        $permissions = [
            // Listings
            PermissionsEnum::VIEW_LISTINGS,
            PermissionsEnum::CREATE_LISTINGS,
            PermissionsEnum::UPDATE_LISTINGS,
            PermissionsEnum::DELETE_LISTINGS,

            // Reservations
            PermissionsEnum::VIEW_RESERVATIONS,
            PermissionsEnum::MODIFY_RESERVATIONS,
            PermissionsEnum::CANCEL_RESERVATIONS,

            // Tasks
            PermissionsEnum::VIEW_TASKS,
            PermissionsEnum::UPDATE_TASKS,
            PermissionsEnum::ASSIGN_TASKS,

            // Issues
            PermissionsEnum::CREATE_ISSUES,
            PermissionsEnum::COMMENT_ON_ISSUES,
            PermissionsEnum::RESOLVE_ISSUES,

            // Admin tools
            PermissionsEnum::IMPERSONATE_USERS,
            PermissionsEnum::ACCESS_NOVA_DASHBOARD,
            PermissionsEnum::VIEW_MESSAGE_LOGS,
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Define roles and assign permissions
        Role::firstOrCreate(['name' => RolesEnum::ADMIN])->syncPermissions(Permission::all());

        Role::firstOrCreate(['name' => RolesEnum::SUPPORT])->syncPermissions([
            PermissionsEnum::VIEW_LISTINGS,
            PermissionsEnum::VIEW_RESERVATIONS,
            PermissionsEnum::CREATE_ISSUES,
            PermissionsEnum::COMMENT_ON_ISSUES,
            PermissionsEnum::RESOLVE_ISSUES,
            PermissionsEnum::IMPERSONATE_USERS,
            PermissionsEnum::VIEW_MESSAGE_LOGS,
            PermissionsEnum::ACCESS_NOVA_DASHBOARD,
        ]);

        Role::firstOrCreate(['name' => RolesEnum::HOST])->syncPermissions([
            PermissionsEnum::VIEW_LISTINGS,
            PermissionsEnum::CREATE_LISTINGS,
            PermissionsEnum::UPDATE_LISTINGS,
            PermissionsEnum::VIEW_RESERVATIONS,
            PermissionsEnum::MODIFY_RESERVATIONS,
            PermissionsEnum::CREATE_ISSUES,
        ]);

        Role::firstOrCreate(['name' => RolesEnum::COHOST])->syncPermissions([
            PermissionsEnum::VIEW_LISTINGS,
            PermissionsEnum::VIEW_RESERVATIONS,
            PermissionsEnum::VIEW_TASKS,
        ]);

        Role::firstOrCreate(['name' => RolesEnum::CLEANER])->syncPermissions([
            PermissionsEnum::VIEW_TASKS,
            PermissionsEnum::UPDATE_TASKS,
        ]);
    }
}
