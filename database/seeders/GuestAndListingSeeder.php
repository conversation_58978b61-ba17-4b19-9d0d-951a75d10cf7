<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Listing;
use App\Models\Reservation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class GuestAndListingSeeder extends Seeder
{
    public function run(): void
    {
        // Create 5 host users
        $hosts = User::factory()
            ->count(5)
            ->create()
            ->each(function ($user) {
                $user->assignRole('host');
            });

        // For each host, create 2–4 listings and reservations
        foreach ($hosts as $host) {
            Listing::factory()
                ->count(rand(2, 4))
                ->for($host)
                ->create()
                ->each(function ($listing) use ($host) {
                    Reservation::factory()
                        ->count(rand(2, 5))
                        ->for($listing)
                        ->create();
                });
        }

        $this->command->info('Seeded hosts, listings, and reservations.');
    }
}