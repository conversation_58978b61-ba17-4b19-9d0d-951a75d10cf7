<?php

namespace Database\Factories;

use App\Models\Listing;
use App\Models\Task;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    protected $model = Task::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['cleaning','maintenance','inspection'];
        $status = $this->faker->randomElement(['pending','in_progress','done']);

        return [
            'listing_id' => Listing::factory(),
            'type'       => $this->faker->randomElement($types),
            'notes'      => $this->faker->optional()->sentence(8),
            'status'     => $status,
            'due_at'     => $this->faker->dateTimeBetween('-1 day', '+3 days'),
        ];
    }
}
