<?php

namespace Database\Factories;

use App\Models\Issue;
use App\Models\IssueComment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IssueComment>
 */
class IssueCommentFactory extends Factory
{
    protected $model = IssueComment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'issue_id' => Issue::factory(),
            'user_id'  => User::factory(),
            'comment'  => $this->faker->sentence(12),
        ];
    }
}
