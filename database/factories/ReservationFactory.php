<?php

namespace Database\Factories;

use App\Models\Listing;
use App\Models\Reservation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reservation>
 */
class ReservationFactory extends Factory
{
    protected $model = Reservation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $checkIn  = $this->faker->dateTimeBetween('+1 days', '+30 days');
        $checkOut = (clone $checkIn)->modify('+'.mt_rand(2,7).' days');

        return [
            'listing_id'  => Listing::factory(),
            'guest_name'  => $this->faker->name,
            'check_in'    => $checkIn,
            'check_out'   => $checkOut,
            'total_price' => $this->faker->randomFloat(2, 200, 2500),
        ];
    }
}
