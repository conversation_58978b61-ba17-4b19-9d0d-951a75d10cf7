<?php

namespace Database\Factories;

use App\Models\Issue;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Issue>
 */
class IssueFactory extends Factory
{
    protected $model = Issue::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id'     => User::factory(),
            'title'       => 'Bug: '.$this->faker->sentence(4),
            'description' => $this->faker->paragraph,
            'status'      => $this->faker->randomElement(['open','in_progress','resolved']),
            'severity'    => $this->faker->randomElement(['low','medium','high']),
        ];
    }
}
