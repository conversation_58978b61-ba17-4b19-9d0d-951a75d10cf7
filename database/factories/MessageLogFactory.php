<?php

namespace Database\Factories;

use App\Models\MessageLog;
use App\Models\MessageRule;
use App\Models\Reservation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageLog>
 */
class MessageLogFactory extends Factory
{
    protected $model = MessageLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(['pending','sent','failed']);

        return [
            'reservation_id'  => Reservation::factory(),
            'message_rule_id' => MessageRule::factory(),
            'status'          => $status,
            'message'         => $this->faker->sentence(10),
            'sent_at'         => $status === 'sent'
                                 ? $this->faker->dateTimeBetween('-1 hour','now')
                                 : null,
            'retry_count'     => $status === 'failed' ? mt_rand(1,3) : 0,
        ];
    }
}
