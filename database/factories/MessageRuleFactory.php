<?php

namespace Database\Factories;

use App\Models\Listing;
use App\Models\MessageRule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageRule>
 */
class MessageRuleFactory extends Factory
{
    protected $model = MessageRule::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'listing_id'    => Listing::factory(),
            'trigger_event' => $this->faker->randomElement([
                'reservation_created', 'check_in', 'check_out'
            ]),
            'template'      => 'Hello {{guest_name}}, welcome to {{listing_title}}!',
            'enabled'       => $this->faker->boolean(90),
        ];
    }
}
